<template>
	<view class="container">
		<view class="header">
			<text class="title">超高频模组测试</text>
		</view>

		<view class="status-section">
			<text class="section-title">状态信息</text>
			<view class="status-item">
				<text>初始化状态: {{ initStatus }}</text>
			</view>
			<view class="status-item">
				<text>当前功率: {{ currentPower }}</text>
			</view>
			<view class="status-item">
				<text>标签数量: {{ tagCount }}</text>
			</view>
		</view>

		<view class="control-section">
			<text class="section-title">控制操作</text>
			<view class="button-row">
				<button type="primary" @click="testInit" :disabled="isInventorying">测试初始化</button>
				<button type="primary" @click="setPowerTest" :disabled="isInventorying">设置功率</button>
			</view>
			<view class="button-row">
				<button type="primary" @click="startInventory" :disabled="isInventorying || !isInitialized">{{ inventoryBtnText
				}}</button>
				<button type="primary" @click="stopInventory" :disabled="!isInventorying">停止盘点</button>
			</view>
			<view class="button-row">
				<button @click="clearTags">清空标签</button>
				<button @click="closeUhf" :disabled="isInventorying">关闭模组</button>
			</view>
		</view>

		<view class="tags-section">
			<text class="section-title">盘点到的标签 ({{ tagList.length }})</text>
			<scroll-view class="tag-list" scroll-y="true">
				<view v-for="(tag, index) in tagList" :key="index" class="tag-item">
					<text class="tag-epc">EPC: {{ tag.epc }}</text>
					<text class="tag-info">次数: {{ tag.count }} | RSSI: {{ tag.rssi }}</text>
				</view>
			</scroll-view>
		</view>

		<view class="log-section">
			<text class="section-title">日志信息</text>
			<scroll-view class="log-list" scroll-y="true">
				<text v-for="(log, index) in logList" :key="index" class="log-item">{{ log }}</text>
			</scroll-view>
		</view>
	</view>
</template>

<script>
// 安全获取插件对象
var hhwUHFController = null;
var globalEvent = null;

try {
	var app = getApp();
	if (app && app.globalData) {
		hhwUHFController = app.globalData.hhwUHFController;
		globalEvent = app.globalData.globalEvent;
	}
} catch (e) {
	console.log('获取插件对象失败:', e);
}

// 字节序列转十六进制字符串
function bytesToHexString (byteArray) {
	return Array.from(byteArray, function (byte) {
		return ('0' + (byte & 0xFF).toString(16)).slice(-2)
	}).join('')
}

export default {
	data () {
		return {
			initStatus: '未初始化',
			currentPower: '未知',
			tagCount: 0,
			isInitialized: false,
			isInventorying: false,
			inventoryBtnText: '开始盘点',
			tagList: [],
			epcList: [],
			logList: []
		}
	},
	onLoad () {
		this.addLog('页面加载完成');
		// 检查插件可用性
		if (!hhwUHFController) {
			this.addLog('警告：UHF插件不可用，请在支持的设备上运行');
			this.initStatus = '插件不可用';
		}
		// 注册事件监听
		if (globalEvent) {
			try {
				globalEvent.addEventListener('uhf_tag_event', (e) => {
					this.handleTagEvent(e);
				});
				this.addLog('事件监听注册成功');
			} catch (e) {
				this.addLog('事件监听注册失败: ' + e.message);
			}
		} else {
			this.addLog('警告：事件监听不可用');
		}
	},
	methods: {
		addLog (message) {
			const time = new Date().toLocaleTimeString();
			this.logList.unshift(`[${time}] ${message}`);
			if (this.logList.length > 50) {
				this.logList.pop();
			}
		},

		testInit () {
			if (!hhwUHFController) {
				this.addLog('错误：UHF插件不可用');
				return;
			}
			this.addLog('开始测试初始化...');
			try {
				hhwUHFController.initUhf(result => {
					this.addLog(`初始化结果: ${result}`);
					this.initStatus = result ? '已初始化' : '初始化失败';
					this.isInitialized = result;
					if (result) {
						this.getPowerTest();
					}
				});
			} catch (e) {
				this.addLog('初始化调用失败: ' + e.message);
			}
		},

		setPowerTest () {
			if (!hhwUHFController) {
				this.addLog('错误：UHF插件不可用');
				return;
			}
			this.addLog('设置功率为30...');
			try {
				hhwUHFController.setPower(30, result => {
					this.addLog(`设置功率结果: ${result}`);
					if (result) {
						this.getPowerTest();
					}
				});
			} catch (e) {
				this.addLog('设置功率调用失败: ' + e.message);
			}
		},

		getPowerTest () {
			if (!hhwUHFController) {
				this.addLog('错误：UHF插件不可用');
				return;
			}
			try {
				hhwUHFController.getPower(power => {
					this.addLog(`当前功率: ${power}`);
					this.currentPower = power;
				});
			} catch (e) {
				this.addLog('获取功率调用失败: ' + e.message);
			}
		},

		startInventory () {
			if (!hhwUHFController) {
				this.addLog('错误：UHF插件不可用');
				return;
			}
			if (!this.isInitialized) {
				this.addLog('请先初始化模组');
				return;
			}

			this.addLog('开始盘点...');
			this.isInventorying = true;
			this.inventoryBtnText = '盘点中...';

			try {
				// 设置session为0（同步盘点）
				hhwUHFController.setGen2session(0, result => {
					this.addLog(`设置session结果: ${result}`);
					if (result) {
						// 开始盘点：同步盘点，不限时，不播放声音
						hhwUHFController.startInventory(false, 0, false);
						this.addLog('盘点已启动');
					} else {
						this.isInventorying = false;
						this.inventoryBtnText = '开始盘点';
					}
				});
			} catch (e) {
				this.addLog('盘点启动失败: ' + e.message);
				this.isInventorying = false;
				this.inventoryBtnText = '开始盘点';
			}
		},

		stopInventory () {
			if (!hhwUHFController) {
				this.addLog('错误：UHF插件不可用');
				return;
			}
			this.addLog('停止盘点...');
			try {
				hhwUHFController.stopInventory(false);
			} catch (e) {
				this.addLog('停止盘点失败: ' + e.message);
			}
		},

		handleTagEvent (e) {
			var result = e.tag_info_list;

			if (result == null) {
				// 接收到停止盘点的回调消息
				var event = e.inventory_event;
				if (event == "stopInventory") {
					this.addLog('收到停止盘点事件');
					this.isInventorying = false;
					this.inventoryBtnText = '开始盘点';
				}
				return;
			}

			// 处理盘点到的标签信息
			for (var i = 0; i < result.length; i++) {
				var epc = bytesToHexString(result[i].EpcId);
				var rssi = result[i].RSSI;

				var index = this.epcList.indexOf(epc);
				if (index == -1) {
					// 新标签
					var tag = {
						epc: epc,
						count: 1,
						rssi: rssi
					};
					this.tagList.push(tag);
					this.epcList.push(epc);
					this.addLog(`发现新标签: ${epc}`);
				} else {
					// 已存在的标签，更新计数
					this.tagList[index].count++;
					this.tagList[index].rssi = rssi;
				}
			}
			this.tagCount = this.tagList.length;
		},

		clearTags () {
			this.tagList = [];
			this.epcList = [];
			this.tagCount = 0;
			this.addLog('已清空标签列表');
		},

		closeUhf () {
			if (!hhwUHFController) {
				this.addLog('错误：UHF插件不可用');
				return;
			}
			this.addLog('关闭超高频模组...');
			try {
				hhwUHFController.closeUhf(result => {
					this.addLog(`关闭模组结果: ${result}`);
					this.initStatus = result ? '已关闭' : '关闭失败';
					this.isInitialized = !result;
					this.currentPower = '未知';
				});
			} catch (e) {
				this.addLog('关闭模组失败: ' + e.message);
			}
		}
	}
}
</script>

<style>
.container {
	padding: 20rpx;
	background-color: #f5f5f5;
}

.header {
	text-align: center;
	margin-bottom: 30rpx;
}

.title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.section-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #666;
	margin-bottom: 20rpx;
}

.status-section,
.control-section,
.tags-section,
.log-section {
	background-color: white;
	padding: 20rpx;
	margin-bottom: 20rpx;
	border-radius: 10rpx;
}

.status-item {
	padding: 10rpx 0;
	border-bottom: 1rpx solid #eee;
}

.button-row {
	display: flex;
	justify-content: space-between;
	margin-bottom: 20rpx;
}

.button-row button {
	flex: 1;
	margin: 0 10rpx;
}

.tag-list,
.log-list {
	height: 300rpx;
	border: 1rpx solid #eee;
	padding: 10rpx;
}

.tag-item {
	padding: 10rpx;
	border-bottom: 1rpx solid #eee;
	margin-bottom: 10rpx;
}

.tag-epc {
	font-size: 24rpx;
	color: #333;
	display: block;
}

.tag-info {
	font-size: 20rpx;
	color: #666;
	display: block;
	margin-top: 5rpx;
}

.log-item {
	font-size: 20rpx;
	color: #666;
	display: block;
	margin-bottom: 5rpx;
	line-height: 1.4;
}
</style>
