<script>
// 平台检测和安全初始化
var main = null;
var hhwUHFController = null;
var globalEvent = null;

// 只在App环境中初始化plus相关功能
if (typeof plus !== 'undefined' && plus.android) {
	try {
		main = plus.android.runtimeMainActivity();
		// ！！！为了保证UHF正常关闭下电，延迟退出APP！！！
		if (uni.getSystemInfoSync().platform == 'android') {
			plus.runtime.quit = function () {
				setTimeout(() => {
					main.finish()
				}, 10)
			};
		}
	} catch (e) {
		console.log('Plus初始化失败:', e);
	}
}

export default {
	globalData: {
		hhwUHFController: null,
		globalEvent: null
	},
	onLaunch: function () {
		console.log('App Launch')
		// 尝试初始化原生插件（只在App环境中有效）
		try {
			if (typeof uni.requireNativePlugin === 'function') {
				this.globalData.hhwUHFController = uni.requireNativePlugin('HL-HHWUHFController');
				this.globalData.globalEvent = uni.requireNativePlugin('globalEvent');
				hhwUHFController = this.globalData.hhwUHFController;
				console.log('原生插件初始化成功');
			} else {
				console.log('当前环境不支持原生插件');
			}
		} catch (e) {
			console.log('原生插件初始化失败:', e);
		}
	},
	onShow: function () {
		console.log('App Show')
		// 只在原生插件可用时执行UHF相关操作
		if (hhwUHFController) {
			try {
				// 开启插件java接口的日志打印
				hhwUHFController.setDebuggable(true, result => {
					console.log("App Show", "setDebuggable: " + result);
				});
				// 初始化超高频（UHF），上电
				var enterTime = Date.now();
				var outTime = enterTime;
				hhwUHFController.initUhf(result => {
					outTime = Date.now();
					console.log("App Show", "initUHF: " + result, "cusTime: " + (outTime - enterTime));
					uni.showToast({
						title: result ? '初始化成功' : '初始化失败',
						icon: "none",
						duration: 1000
					});
				});
			} catch (e) {
				console.log('UHF初始化失败:', e);
			}
		} else {
			console.log('UHF插件不可用，跳过初始化');
		}
	},
	onHide: function () {
		console.log('App Hide')
		// 只在原生插件可用时执行UHF关闭操作
		if (hhwUHFController) {
			try {
				// 关闭超高频（UHF），下电
				hhwUHFController.closeUhf(result => {
					console.log("App Hide", "closeUhf: " + result);
					uni.showToast({
						title: result ? '关闭成功' : '关闭失败',
						icon: "none",
						duration: 1000
					});
				});
			} catch (e) {
				console.log('UHF关闭失败:', e);
			}
		} else {
			console.log('UHF插件不可用，跳过关闭操作');
		}
	}
}
</script>

<style>
/*每个页面公共css */
</style>
